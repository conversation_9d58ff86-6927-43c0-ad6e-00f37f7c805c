# Configuration des APIs externes
SERPAPI_KEY=your_serpapi_key_here
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Configuration de l'application
DEBUG=True
LOG_LEVEL=INFO

# Configuration de recherche
MAX_SEARCH_RESULTS=20
DEFAULT_SEARCH_RESULTS=5
SEARCH_TIMEOUT=30

# Configuration Qdrant (si utilisé avec un serveur externe)
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_API_KEY=

# Configuration CORS (optionnel)
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:8080

# Configuration pour le déploiement
PORT=8000
HOST=0.0.0.0
