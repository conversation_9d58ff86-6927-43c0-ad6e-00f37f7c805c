{"version": 3, "file": "background.js", "sourceRoot": "", "sources": ["../../../src/render/background.ts"], "names": [], "mappings": ";;;AAGA,+EAAgG;AAChG,mCAAgC;AAEhC,oEAA8G;AAC9G,+CAA4D;AAC5D,2CAAoD;AAI7C,IAAM,kCAAkC,GAAG,UAC9C,gBAAmC,EACnC,OAAyB;IAEzB,IAAI,gBAAgB,uBAAiC,EAAE;QACnD,OAAO,OAAO,CAAC,MAAM,CAAC;KACzB;IAED,IAAI,gBAAgB,wBAAkC,EAAE;QACpD,OAAO,uBAAU,CAAC,OAAO,CAAC,CAAC;KAC9B;IAED,OAAO,uBAAU,CAAC,OAAO,CAAC,CAAC;AAC/B,CAAC,CAAC;AAbW,QAAA,kCAAkC,sCAa7C;AAEK,IAAM,+BAA+B,GAAG,UAAC,cAA+B,EAAE,OAAyB;IACtG,IAAI,cAAc,uBAA+B,EAAE;QAC/C,OAAO,OAAO,CAAC,MAAM,CAAC;KACzB;IAED,IAAI,cAAc,wBAAgC,EAAE;QAChD,OAAO,uBAAU,CAAC,OAAO,CAAC,CAAC;KAC9B;IAED,OAAO,uBAAU,CAAC,OAAO,CAAC,CAAC;AAC/B,CAAC,CAAC;AAVW,QAAA,+BAA+B,mCAU1C;AAEK,IAAM,4BAA4B,GAAG,UACxC,SAA2B,EAC3B,KAAa,EACb,aAA4D;IAE5D,IAAM,yBAAyB,GAAG,0CAAkC,CAChE,kCAA0B,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,EAAE,KAAK,CAAC,EACpE,SAAS,CACZ,CAAC;IAEF,IAAM,sBAAsB,GAAG,uCAA+B,CAC1D,kCAA0B,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,EAAE,KAAK,CAAC,EAClE,SAAS,CACZ,CAAC;IAEF,IAAM,mBAAmB,GAAG,+BAAuB,CAC/C,kCAA0B,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,EAAE,KAAK,CAAC,EAClE,aAAa,EACb,yBAAyB,CAC5B,CAAC;IAEK,IAAA,SAAS,GAAgB,mBAAmB,GAAnC,EAAE,UAAU,GAAI,mBAAmB,GAAvB,CAAwB;IAEpD,IAAM,QAAQ,GAAG,4CAAwB,CACrC,kCAA0B,CAAC,SAAS,CAAC,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC,EACtE,yBAAyB,CAAC,KAAK,GAAG,SAAS,EAC3C,yBAAyB,CAAC,MAAM,GAAG,UAAU,CAChD,CAAC;IAEF,IAAM,IAAI,GAAG,qCAA6B,CACtC,kCAA0B,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,EAAE,KAAK,CAAC,EACpE,QAAQ,EACR,mBAAmB,EACnB,yBAAyB,EACzB,sBAAsB,CACzB,CAAC;IAEF,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAExE,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;AAC3D,CAAC,CAAC;AAzCW,QAAA,4BAA4B,gCAyCvC;AAEK,IAAM,MAAM,GAAG,UAAC,KAAe,IAAc,OAAA,qBAAY,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,iCAAe,CAAC,IAAI,EAA3D,CAA2D,CAAC;AAAnG,QAAA,MAAM,UAA6F;AAEhH,IAAM,iBAAiB,GAAG,UAAC,KAAoB,IAAsB,OAAA,OAAO,KAAK,KAAK,QAAQ,EAAzB,CAAyB,CAAC;AAExF,IAAM,uBAAuB,GAAG,UACnC,IAA0B,EAC1B,EAAqG,EACrG,MAAc;QADb,cAAc,QAAA,EAAE,eAAe,QAAA,EAAE,mBAAmB,QAAA;IAG9C,IAAA,KAAK,GAAY,IAAI,GAAhB,EAAE,MAAM,GAAI,IAAI,GAAR,CAAS;IAE7B,IAAI,CAAC,KAAK,EAAE;QACR,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACjB;IAED,IAAI,sCAAkB,CAAC,KAAK,CAAC,IAAI,MAAM,IAAI,sCAAkB,CAAC,MAAM,CAAC,EAAE;QACnE,OAAO,CAAC,oCAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,oCAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;KAC3F;IAED,IAAM,sBAAsB,GAAG,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;IAEtE,IAAI,qBAAY,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,iCAAe,CAAC,OAAO,IAAI,KAAK,CAAC,KAAK,KAAK,iCAAe,CAAC,KAAK,CAAC,EAAE;QAC3G,IAAI,iBAAiB,CAAC,mBAAmB,CAAC,EAAE;YACxC,IAAM,WAAW,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;YAEjD,OAAO,WAAW,GAAG,mBAAmB,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,iCAAe,CAAC,KAAK,CAAC;gBAChF,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,GAAG,mBAAmB,CAAC;gBACpD,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,mBAAmB,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;SAC9D;QAED,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;KACxC;IAED,IAAM,iBAAiB,GAAG,iBAAiB,CAAC,cAAc,CAAC,CAAC;IAC5D,IAAM,kBAAkB,GAAG,iBAAiB,CAAC,eAAe,CAAC,CAAC;IAC9D,IAAM,sBAAsB,GAAG,iBAAiB,IAAI,kBAAkB,CAAC;IAEvE,+CAA+C;IAC/C,IAAI,cAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,cAAM,CAAC,MAAM,CAAC,CAAC,EAAE;QAC9C,kGAAkG;QAClG,IAAI,iBAAiB,IAAI,kBAAkB,EAAE;YACzC,OAAO,CAAC,cAAwB,EAAE,eAAyB,CAAC,CAAC;SAChE;QAED,6EAA6E;QAC7E,gEAAgE;QAEhE,IAAI,CAAC,sBAAsB,IAAI,CAAC,sBAAsB,EAAE;YACpD,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;SACxC;QAED,uIAAuI;QAEvI,8IAA8I;QAC9I,+FAA+F;QAC/F,IAAI,sBAAsB,IAAI,sBAAsB,EAAE;YAClD,IAAM,OAAK,GAAG,iBAAiB;gBAC3B,CAAC,CAAE,cAAyB;gBAC5B,CAAC,CAAE,eAA0B,GAAI,mBAA8B,CAAC;YACpE,IAAM,QAAM,GAAG,kBAAkB;gBAC7B,CAAC,CAAE,eAA0B;gBAC7B,CAAC,CAAE,cAAyB,GAAI,mBAA8B,CAAC;YACnE,OAAO,CAAC,OAAK,EAAE,QAAM,CAAC,CAAC;SAC1B;QAED,kFAAkF;QAClF,0GAA0G;QAC1G,IAAM,OAAK,GAAG,iBAAiB,CAAC,CAAC,CAAE,cAAyB,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;QAC5E,IAAM,QAAM,GAAG,kBAAkB,CAAC,CAAC,CAAE,eAA0B,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;QAChF,OAAO,CAAC,OAAK,EAAE,QAAM,CAAC,CAAC;KAC1B;IAED,qFAAqF;IACrF,qGAAqG;IACrG,IAAI,sBAAsB,EAAE;QACxB,IAAI,OAAK,GAAG,CAAC,CAAC;QACd,IAAI,QAAM,GAAG,CAAC,CAAC;QACf,IAAI,sCAAkB,CAAC,KAAK,CAAC,EAAE;YAC3B,OAAK,GAAG,oCAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;SACjD;aAAM,IAAI,sCAAkB,CAAC,MAAM,CAAC,EAAE;YACnC,QAAM,GAAG,oCAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;SACpD;QAED,IAAI,cAAM,CAAC,KAAK,CAAC,EAAE;YACf,OAAK,GAAG,QAAM,GAAI,mBAA8B,CAAC;SACpD;aAAM,IAAI,CAAC,MAAM,IAAI,cAAM,CAAC,MAAM,CAAC,EAAE;YAClC,QAAM,GAAG,OAAK,GAAI,mBAA8B,CAAC;SACpD;QAED,OAAO,CAAC,OAAK,EAAE,QAAM,CAAC,CAAC;KAC1B;IAED,wFAAwF;IACxF,6FAA6F;IAC7F,4DAA4D;IAC5D,6EAA6E;IAE7E,IAAI,KAAK,GAAG,IAAI,CAAC;IACjB,IAAI,MAAM,GAAG,IAAI,CAAC;IAElB,IAAI,sCAAkB,CAAC,KAAK,CAAC,EAAE;QAC3B,KAAK,GAAG,oCAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;KACjD;SAAM,IAAI,MAAM,IAAI,sCAAkB,CAAC,MAAM,CAAC,EAAE;QAC7C,MAAM,GAAG,oCAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;KACpD;IAED,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,CAAC,MAAM,IAAI,cAAM,CAAC,MAAM,CAAC,CAAC,EAAE;QAC/C,MAAM;YACF,iBAAiB,IAAI,kBAAkB;gBACnC,CAAC,CAAC,CAAC,KAAK,GAAI,cAAyB,CAAC,GAAI,eAA0B;gBACpE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;KAC3B;IAED,IAAI,MAAM,KAAK,IAAI,IAAI,cAAM,CAAC,KAAK,CAAC,EAAE;QAClC,KAAK;YACD,iBAAiB,IAAI,kBAAkB;gBACnC,CAAC,CAAC,CAAC,MAAM,GAAI,eAA0B,CAAC,GAAI,cAAyB;gBACrE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;KAC1B;IAED,IAAI,KAAK,KAAK,IAAI,IAAI,MAAM,KAAK,IAAI,EAAE;QACnC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;KAC1B;IAED,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;AACvE,CAAC,CAAC;AAzHW,QAAA,uBAAuB,2BAyHlC;AAEK,IAAM,0BAA0B,GAAG,UAAI,MAAW,EAAE,KAAa;IACpE,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAC5B,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;QAC9B,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;KACpB;IAED,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAPW,QAAA,0BAA0B,8BAOrC;AAEK,IAAM,6BAA6B,GAAG,UACzC,MAAyB,EACzB,EAAwB,EACxB,EAAiC,EACjC,yBAAiC,EACjC,sBAA8B;QAH7B,CAAC,QAAA,EAAE,CAAC,QAAA;QACJ,KAAK,QAAA,EAAE,MAAM,QAAA;IAId,QAAQ,MAAM,EAAE;QACZ;YACI,OAAO;gBACH,IAAI,eAAM,CAAC,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gBACrG,IAAI,eAAM,CACN,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,IAAI,GAAG,yBAAyB,CAAC,KAAK,CAAC,EAC5E,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,GAAG,GAAG,CAAC,CAAC,CAChD;gBACD,IAAI,eAAM,CACN,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,IAAI,GAAG,yBAAyB,CAAC,KAAK,CAAC,EAC5E,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,yBAAyB,CAAC,GAAG,GAAG,CAAC,CAAC,CACzD;gBACD,IAAI,eAAM,CACN,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,IAAI,CAAC,EAC1C,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,yBAAyB,CAAC,GAAG,GAAG,CAAC,CAAC,CACzD;aACJ,CAAC;QACN;YACI,OAAO;gBACH,IAAI,eAAM,CAAC,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC;gBACrG,IAAI,eAAM,CACN,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,EACtD,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAC5C;gBACD,IAAI,eAAM,CACN,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,EACtD,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,MAAM,GAAG,yBAAyB,CAAC,GAAG,CAAC,CAC/E;gBACD,IAAI,eAAM,CACN,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,IAAI,GAAG,CAAC,CAAC,EAC9C,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,MAAM,GAAG,yBAAyB,CAAC,GAAG,CAAC,CAC/E;aACJ,CAAC;QACN;YACI,OAAO;gBACH,IAAI,eAAM,CACN,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,IAAI,GAAG,CAAC,CAAC,EAC9C,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,GAAG,GAAG,CAAC,CAAC,CAChD;gBACD,IAAI,eAAM,CACN,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,EACtD,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,GAAG,GAAG,CAAC,CAAC,CAChD;gBACD,IAAI,eAAM,CACN,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,EACtD,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,CACzD;gBACD,IAAI,eAAM,CACN,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,IAAI,GAAG,CAAC,CAAC,EAC9C,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,CACzD;aACJ,CAAC;QACN;YACI,OAAO;gBACH,IAAI,eAAM,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;gBAC3F,IAAI,eAAM,CACN,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,IAAI,GAAG,sBAAsB,CAAC,KAAK,CAAC,EACtE,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,GAAG,CAAC,CACzC;gBACD,IAAI,eAAM,CACN,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,IAAI,GAAG,sBAAsB,CAAC,KAAK,CAAC,EACtE,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,MAAM,GAAG,sBAAsB,CAAC,GAAG,CAAC,CACzE;gBACD,IAAI,eAAM,CACN,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,EACvC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,MAAM,GAAG,sBAAsB,CAAC,GAAG,CAAC,CACzE;aACJ,CAAC;KACT;AACL,CAAC,CAAC;AA5EW,QAAA,6BAA6B,iCA4ExC"}