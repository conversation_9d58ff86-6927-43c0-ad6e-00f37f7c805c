{"version": 3, "file": "features.js", "sourceRoot": "", "sources": ["../../../src/core/features.ts"], "names": [], "mappings": ";;;AAAA,iDAA2D;AAE3D,IAAM,eAAe,GAAG,UAAC,QAAkB;IACvC,IAAM,WAAW,GAAG,GAAG,CAAC;IAExB,IAAI,QAAQ,CAAC,WAAW,EAAE;QACtB,IAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QACrC,IAAI,KAAK,CAAC,qBAAqB,EAAE;YAC7B,IAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;YACxD,WAAW,CAAC,KAAK,CAAC,MAAM,GAAM,WAAW,OAAI,CAAC;YAC9C,WAAW,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;YACpC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YAEvC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YAC9B,IAAM,WAAW,GAAG,KAAK,CAAC,qBAAqB,EAAE,CAAC;YAClD,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACnD,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YACvC,IAAI,WAAW,KAAK,WAAW,EAAE;gBAC7B,OAAO,IAAI,CAAC;aACf;SACJ;KACJ;IAED,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAEF,IAAM,gBAAgB,GAAG,UAAC,QAAkB;IACxC,IAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;IACxD,WAAW,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC;IACjC,WAAW,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;IACpC,WAAW,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC;IACpC,WAAW,CAAC,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC;IACxC,WAAW,CAAC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;IACtC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IACvC,IAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;IAErC,WAAW,CAAC,SAAS,GAAG,OAAO,EAAE,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAEtF,IAAM,IAAI,GAAG,WAAW,CAAC,UAAkB,CAAC;IAE5C,IAAM,QAAQ,GAAG,6BAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,8BAAa,CAAC,CAAC,CAAC,EAAhB,CAAgB,CAAC,CAAC;IACtE,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,IAAI,GAAY,EAAa,CAAC;IAElC,kFAAkF;IAClF,IAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,UAAC,IAAI,EAAE,CAAC;QACpC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC7B,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QACzC,IAAM,IAAI,GAAG,KAAK,CAAC,qBAAqB,EAAE,CAAC;QAE3C,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;QACtB,IAAM,UAAU,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAEtD,IAAI,GAAG,IAAI,CAAC;QACZ,IAAI,CAAC,KAAK,CAAC,EAAE;YACT,OAAO,IAAI,CAAC;SACf;QAED,OAAO,UAAU,CAAC;IACtB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IACvC,OAAO,QAAQ,CAAC;AACpB,CAAC,CAAC;AAEF,IAAM,QAAQ,GAAG,cAAe,OAAA,OAAO,IAAI,KAAK,EAAE,CAAC,WAAW,KAAK,WAAW,EAA9C,CAA8C,CAAC;AAE/E,IAAM,gBAAgB,GAAG,cAAe,OAAA,OAAO,IAAI,cAAc,EAAE,CAAC,YAAY,KAAK,QAAQ,EAArD,CAAqD,CAAC;AAE9F,IAAM,OAAO,GAAG,UAAC,QAAkB;IAC/B,IAAM,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC;IACxB,IAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAChD,IAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACpC,IAAI,CAAC,GAAG,EAAE;QACN,OAAO,KAAK,CAAC;KAChB;IAED,GAAG,CAAC,GAAG,GAAG,mEAAmE,CAAC;IAE9E,IAAI;QACA,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzB,MAAM,CAAC,SAAS,EAAE,CAAC;KACtB;IAAC,OAAO,CAAC,EAAE;QACR,OAAO,KAAK,CAAC;KAChB;IACD,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,IAAM,YAAY,GAAG,UAAC,IAAuB;IACzC,OAAA,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG;AAApE,CAAoE,CAAC;AAEzE,IAAM,iBAAiB,GAAG,UAAC,QAAkB;IACzC,IAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAChD,IAAM,IAAI,GAAG,GAAG,CAAC;IACjB,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;IACpB,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;IACrB,IAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACpC,IAAI,CAAC,GAAG,EAAE;QACN,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KAChC;IACD,GAAG,CAAC,SAAS,GAAG,gBAAgB,CAAC;IACjC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAE/B,IAAM,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC;IACxB,IAAM,aAAa,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;IACzC,GAAG,CAAC,GAAG,GAAG,aAAa,CAAC;IACxB,IAAM,GAAG,GAAG,8BAAsB,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;IAC1D,GAAG,CAAC,SAAS,GAAG,KAAK,CAAC;IACtB,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAE/B,OAAO,yBAAiB,CAAC,GAAG,CAAC;SACxB,IAAI,CAAC,UAAC,GAAqB;QACxB,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzB,IAAM,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC;QACrD,GAAG,CAAC,SAAS,GAAG,KAAK,CAAC;QACtB,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAE/B,IAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,SAAO,aAAa,MAAG,CAAC;QACrD,IAAI,CAAC,KAAK,CAAC,MAAM,GAAM,IAAI,OAAI,CAAC;QAChC,iDAAiD;QACjD,OAAO,YAAY,CAAC,IAAI,CAAC;YACrB,CAAC,CAAC,yBAAiB,CAAC,8BAAsB,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;YACnE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC,CAAC;SACD,IAAI,CAAC,UAAC,GAAqB;QACxB,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzB,yCAAyC;QACzC,OAAO,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IACjE,CAAC,CAAC;SACD,KAAK,CAAC,cAAM,OAAA,KAAK,EAAL,CAAK,CAAC,CAAC;AAC5B,CAAC,CAAC;AAEK,IAAM,sBAAsB,GAAG,UAClC,KAAa,EACb,MAAc,EACd,CAAS,EACT,CAAS,EACT,IAAU;IAEV,IAAM,KAAK,GAAG,4BAA4B,CAAC;IAC3C,IAAM,GAAG,GAAG,QAAQ,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACnD,IAAM,aAAa,GAAG,QAAQ,CAAC,eAAe,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;IACvE,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;IACpD,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;IAEtD,aAAa,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACpD,aAAa,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IACrD,aAAa,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;IACtD,aAAa,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;IACtD,aAAa,CAAC,cAAc,CAAC,IAAI,EAAE,2BAA2B,EAAE,MAAM,CAAC,CAAC;IACxE,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;IAE/B,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAEhC,OAAO,GAAG,CAAC;AACf,CAAC,CAAC;AAvBW,QAAA,sBAAsB,0BAuBjC;AAEK,IAAM,iBAAiB,GAAG,UAAC,GAAS;IACvC,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;QAC/B,IAAM,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC;QACxB,GAAG,CAAC,MAAM,GAAG,cAAM,OAAA,OAAO,CAAC,GAAG,CAAC,EAAZ,CAAY,CAAC;QAChC,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC;QAErB,GAAG,CAAC,GAAG,GAAG,sCAAoC,kBAAkB,CAAC,IAAI,aAAa,EAAE,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAG,CAAC;IACnH,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AARW,QAAA,iBAAiB,qBAQ5B;AAEW,QAAA,QAAQ,GAAG;IACpB,IAAI,oBAAoB;QACpB,YAAY,CAAC;QACb,IAAM,KAAK,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;QACxC,MAAM,CAAC,cAAc,CAAC,gBAAQ,EAAE,sBAAsB,EAAE,EAAC,KAAK,OAAA,EAAC,CAAC,CAAC;QACjE,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,IAAI,qBAAqB;QACrB,YAAY,CAAC;QACb,IAAM,KAAK,GAAG,gBAAQ,CAAC,oBAAoB,IAAI,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAC1E,MAAM,CAAC,cAAc,CAAC,gBAAQ,EAAE,uBAAuB,EAAE,EAAC,KAAK,OAAA,EAAC,CAAC,CAAC;QAClE,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,IAAI,mBAAmB;QACnB,YAAY,CAAC;QACb,IAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;QAChC,MAAM,CAAC,cAAc,CAAC,gBAAQ,EAAE,qBAAqB,EAAE,EAAC,KAAK,OAAA,EAAC,CAAC,CAAC;QAChE,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,IAAI,6BAA6B;QAC7B,YAAY,CAAC;QACb,IAAM,KAAK,GACP,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,UAAU;YAClE,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YAC7B,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACjC,MAAM,CAAC,cAAc,CAAC,gBAAQ,EAAE,+BAA+B,EAAE,EAAC,KAAK,OAAA,EAAC,CAAC,CAAC;QAC1E,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,IAAI,mBAAmB;QACnB,YAAY,CAAC;QACb,IAAM,KAAK,GAAG,QAAQ,EAAE,CAAC;QACzB,MAAM,CAAC,cAAc,CAAC,gBAAQ,EAAE,qBAAqB,EAAE,EAAC,KAAK,OAAA,EAAC,CAAC,CAAC;QAChE,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,IAAI,qBAAqB;QACrB,YAAY,CAAC;QACb,IAAM,KAAK,GAAG,gBAAgB,EAAE,CAAC;QACjC,MAAM,CAAC,cAAc,CAAC,gBAAQ,EAAE,uBAAuB,EAAE,EAAC,KAAK,OAAA,EAAC,CAAC,CAAC;QAClE,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,IAAI,gBAAgB;QAChB,YAAY,CAAC;QACb,IAAM,KAAK,GAAG,iBAAiB,IAAI,IAAI,cAAc,EAAE,CAAC;QACxD,MAAM,CAAC,cAAc,CAAC,gBAAQ,EAAE,kBAAkB,EAAE,EAAC,KAAK,OAAA,EAAC,CAAC,CAAC;QAC7D,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,IAAI,gCAAgC;QAChC,YAAY,CAAC;QACb,8DAA8D;QAC9D,IAAM,KAAK,GAAG,CAAC,CAAC,CAAC,OAAO,IAAI,KAAK,WAAW,IAAK,IAAY,CAAC,SAAS,CAAC,CAAC;QACzE,MAAM,CAAC,cAAc,CAAC,gBAAQ,EAAE,kCAAkC,EAAE,EAAC,KAAK,OAAA,EAAC,CAAC,CAAC;QAC7E,OAAO,KAAK,CAAC;IACjB,CAAC;CACJ,CAAC"}