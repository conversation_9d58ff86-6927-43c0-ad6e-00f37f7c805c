# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [1.0.3](https://github.com/niklasvh/text-segmentation/compare/v1.0.2...v1.0.3) (2022-01-22)


### fix

* source maps ([4108278](https://github.com/niklasvh/text-segmentation/commit/4108278c278b271543e9a997dbf14ce14e946fe7))



## [1.0.2](https://github.com/niklasvh/text-segmentation/compare/v1.0.1...v1.0.2) (2021-08-10)


### deps

* update base64-arraybuffer ([3804223](https://github.com/niklasvh/text-segmentation/commit/3804223855fa4f37e8c784dca5ce5113c33d3e27))



## [1.0.1](https://github.com/niklasvh/text-segmentation/compare/v1.0.0...v1.0.1) (2021-08-09)


### docs

* add readme ([c3f1dd3](https://github.com/niklasvh/text-segmentation/commit/c3f1dd31ef4880ad7b8ecab5155f7362d4d652d3))

### feat

* expose fromCodePoint / toCodePoints ([a497aeb](https://github.com/niklasvh/text-segmentation/commit/a497aeb75255fec597b8c4b0803e3b57d6a06a25))



# 1.0.0 (2021-08-09)


### feat

* add grapheme breaker trie ([0e5a06b](https://github.com/niklasvh/text-segmentation/commit/0e5a06b4ab7f1eef9cf7b01fc47bdb270c5704c0))
* implement grapheme breaker ([7e065b5](https://github.com/niklasvh/text-segmentation/commit/7e065b5b2484d2dcd06efc487d938289c197fee0))
* implement splitter ([8a52b31](https://github.com/niklasvh/text-segmentation/commit/8a52b318368ea994b245daf4ac056319ee697f24))
