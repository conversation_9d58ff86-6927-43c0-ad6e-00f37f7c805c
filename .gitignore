# Fichiers de configuration sensibles
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Clés API et secrets
*.key
*.pem
secrets/

# Dépendances Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Environnements virtuels Python
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Dépendances Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Build React
my-project/build/
my-project/dist/

# Logs
logs
*.log

# Fichiers temporaires
*.tmp
*.temp
.cache/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Base de données locale
*.db
*.sqlite
*.sqlite3

# Fichiers de test
test_uploads/
temp_files/

# Documentation générée
docs/_build/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Qdrant data (si utilisé localement)
qdrant_storage/
