{"version": 3, "file": "webworker.iterable.js", "sourceRoot": "", "sources": ["../../src/lib/webworker.iterable.ts"], "names": [], "mappings": ";AAAA,wCAAwC;AACxC,gCAAgC;AAChC,mEAAmE;AACnE,uDAAuD;;;AAGvD,+CAAqC;AAExB,QAAA,kBAAkB,GAAG;IAChC,KAAK,EAAE,kBAAI;IACX,UAAU,EAAE,kBAAI;IAChB,uBAAuB,EAAE,kBAAI;IAC7B,aAAa,EAAE,kBAAI;IACnB,QAAQ,EAAE,kBAAI;IACd,WAAW,EAAE,kBAAI;IACjB,QAAQ,EAAE,kBAAI;IACd,OAAO,EAAE,kBAAI;IACb,WAAW,EAAE,kBAAI;IACjB,cAAc,EAAE,kBAAI;IACpB,YAAY,EAAE,kBAAI;IAClB,YAAY,EAAE,kBAAI;IAClB,eAAe,EAAE,kBAAI;IACrB,kBAAkB,EAAE,kBAAI;IACxB,gBAAgB,EAAE,kBAAI;IACtB,0BAA0B,EAAE,kBAAI;IAChC,+BAA+B,EAAE,kBAAI;IACrC,yBAAyB,EAAE,kBAAI;IAC/B,8BAA8B,EAAE,kBAAI;CACS,CAAC"}