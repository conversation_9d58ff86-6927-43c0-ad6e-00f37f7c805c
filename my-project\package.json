{"name": "my-project", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.9.0", "framer-motion": "^12.12.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.511.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-firebase-hooks": "^5.1.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.0", "react-router-hash-link": "^2.4.3", "react-scripts": "5.0.1", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.3.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.17"}}